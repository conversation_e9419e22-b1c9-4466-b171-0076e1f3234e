<template>
  <el-dialog :visible.sync="dialogVisible" width="750px" title="定时管理" @close="handleClose">
    <div v-if="cronList.length === 0" class="empty-content">
      <el-alert title="暂无定时任务待生效" type="info" :closable="false" show-icon />
    </div>

    <div v-else>
      <div v-for="item in cronList" :key="item.id" class="cron-item">
        <div class="card-header">
          <section>
            <span style="margin-right: 10px;">定时{{ item.type === 15 ? '开启' : '结束' }}</span>
            <span>{{ item.type === 15 ? '到达定时开启时间后，实验会自动开启，定时开启待生效期间，实验不支持编辑。' : '到达定时结束时间后，实验会自动结束，定时结束生效期间，可随时取消定时任务。'
            }}</span>
          </section>
          <el-button type="text" icon="el-icon-delete" @click="removeCron(item)" class="remove-btn"></el-button>
        </div>
        <div class="time-section">
          <el-date-picker v-model="item.taskTime" type="datetime" value-format="timestamp" placeholder="选择开启时间"
            :picker-options="pickerOptions" />
          <span class="time-tip">实验会在{{ formatTime(item.taskTime) }}{{ item.type === 15 ? '开启' : '结束' }}运行！</span>
        </div>
        <el-alert v-if="hasRunningChild && item.type === 40"
          title="当前的实验关联了子实验，实验结束后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动！" type="warning" :closable="false" />
      </div>
    </div>
    <div class="action-buttons" slot="footer">
      <el-button @click="handleClose()">取消</el-button>
      <el-button type="primary" @click="saveCron()">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import dayjs from 'dayjs';
import { formatFormData } from '@/utils/util';

export default {
  name: 'ControlCron',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      cronList: [],
      hasRunningChild: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      }
    };
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal;
      if (newVal && this.id) {
        this.getCronList();
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
      this.$emit('update:visible', false);
    },

    async getCronList() {
      try {
        const res = await this.$service.get('VIEWCRON', {
          experimentId: this.id
        });
        this.cronList = (res.list || []).map(item => ({
          ...item,
          taskTime: item.taskTime * 1000 // 转换为毫秒
        }));
        this.hasRunningChild = res.hasRunningChild;
      } catch (error) {
        console.error('获取定时任务失败:', error);
      }
    },

    removeCron(item) {
      const message = item.type === 15 ? '确定移除定时开启任务吗？' : '确定移除定时结束任务吗？';
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除接口
        this.deleteCron(item.id);
      });
    },

    async deleteCron(id) {
      const data = formatFormData({
        id
      })
      try {
        await this.$service.post('DELETECRON', data);
        this.$message.success('删除成功');
        this.handleClose()
      } catch (error) {
        this.$message.error('删除失败');
      }
    },

    async saveCron() {
      try {
        // 时间更新逻辑
        const item = this.cronList[0]
        const data = formatFormData({
          taskTime: Math.floor(item.taskTime / 1000),
          id: item.id,
          experimentId: item.experimentId
        })
        await this.$service.post('UPDATECRON', data);
        this.$message.success('保存成功');
        this.handleClose();
      } catch (error) {
        this.$message.error('保存失败');
      }
    },

    formatTime(timestamp) {
      return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
</script>

<style scoped>
.empty-content {
  text-align: center;
  padding: 40px 0;
}

.cron-item {
  margin-bottom: 20px;
  padding: 0px;
  border-radius: 4px;
}

.card-header {
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #42c57a;
  padding: 12px;
  color: white;
  font-size: 13px;
}

.remove-btn {
  padding: 0;
  font-size: 16px;
  color: white;
}

.time-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border: 1px solid #e1e3e8;
  border-top: none;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}
</style>