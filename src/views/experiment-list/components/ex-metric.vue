<template>
  <div class="steps-content">
    <h3>流量设置</h3>
    <section @click="handleDocumentClick('exclusiveGroup')">
      <template v-if="isAdd">
        <div class="mutu-con">
          <span class="is-mutu">是否互斥实验：</span>
          <el-radio-group v-model="mutuType" @change="handelchangeMutu">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
          <div class="s-wrap">
            <el-select ref="select" v-model="addData.newExclusiveId" v-if="mutuType == 1" @change="freeFlowChange">
              <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                :disabled="item.freeFlow == 0">
                <span class="op-name" style="float: left">{{ item.displayName }}</span>
                <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
              </el-option>
            </el-select>
            <span class="free-con" v-if="mutuType == 1 && addData.newExclusiveId">
              {{ `剩余${lastFlow}%` }}
            </span>
          </div>
          <el-button type="text" class="create-mutu" @click="createMutu" v-if="mutuType == 1">
            新建互斥组
          </el-button>
        </div>
      </template>
      <template v-if="isCheck">
        <div class="mutu-con">
          <span class="is-mutu">是否互斥实验：</span>
          <el-radio-group v-model="addData.exclusiveId > 0 ? 1 : 0" :disabled="true">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
          <div class="s-wrap">
            <el-select ref="select" v-model="addData.exclusiveId" v-if="addData.exclusiveId" @change="freeFlowChange"
              :disabled="true">
              <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                :disabled="item.freeFlow == 0">
                <span class="op-name" style="float: left">{{ item.displayName }}</span>
                <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
              </el-option>
            </el-select>
            <span class="free-con" v-if="addData.exclusiveId"> {{ `剩余${lastFlow}%` }} </span>
          </div>
        </div>
      </template>
      <template v-if="isEdit">
        <template v-if="statusIsRun()">
          <!-- 如果已经选择了互斥组 -->
          <template v-if="addData.exclusiveId">
            <div class="mutu-con">
              <span class="is-mutu">是否互斥实验：</span>
              <el-radio-group v-model="mutuType" :disabled="true">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
              <div class="s-wrap">
                <el-select ref="select" v-model="addData.exclusiveId" v-if="mutuType == 1" @change="freeFlowChange"
                  :disabled="true">
                  <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                    :disabled="item.freeFlow == 0">
                    <span class="op-name" style="float: left">{{ item.displayName }}</span>
                    <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
                  </el-option>
                </el-select>
                <span class="free-con">{{ `剩余${lastFlow}%` }}</span>
              </div>
            </div>
          </template>
          <!-- 如果没有选择了互斥组-->
          <template v-else>
            <!-- 如果原发布中的实验不存在互斥组，创建继承实验的时候不能修改 -->
            <template v-if="isExtend">
              <div class="mutu-con">
                <span class="is-mutu">是否互斥实验：</span>
                <el-radio-group v-model="mutuType" :disabled="true">
                  <el-radio :label="0">否</el-radio>
                  <el-radio :label="1">是</el-radio>
                </el-radio-group>
              </div>
            </template>
            <template v-else>
              <div class="mutu-con">
                <span class="is-mutu">是否互斥实验：</span>
                <el-radio-group v-model="mutuType" @change="handelchangeMutu">
                  <el-radio :label="0">否</el-radio>
                  <el-radio :label="1">是</el-radio>
                </el-radio-group>
                <div class="s-wrap">
                  <el-select ref="select" v-model="addData.newExclusiveId" v-if="mutuType == 1"
                    @change="freeFlowChange">
                    <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                      :disabled="item.freeFlow == 0">
                      <span class="op-name" style="float: left">{{ item.displayName }}</span>
                      <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
                    </el-option>
                  </el-select>
                  <span class="free-con" v-if="mutuType == 1 && addData.newExclusiveId">
                    {{ `剩余${lastFlow}%` }}
                  </span>
                </div>

                <el-button type="text" class="create-mutu" @click="createMutu" v-if="mutuType == 1">
                  新建互斥组
                </el-button>
              </div>
            </template>
          </template>
        </template>
        <template v-else-if="statusIsAwaitPub()">
          <!-- 如果已经选择了互斥组 -->
          <template v-if="addData.exclusiveId">
            <div class="mutu-con">
              <span class="is-mutu">是否互斥实验：</span>
              <el-radio-group v-model="mutuType" :disabled="true">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
              <div class="s-wrap">
                <el-select ref="select" v-model="addData.exclusiveId" v-if="mutuType == 1" @change="freeFlowChange"
                  :disabled="true">
                  <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                    :disabled="item.freeFlow == 0">
                    <span class="op-name" style="float: left">{{ item.displayName }}</span>
                    <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
                  </el-option>
                </el-select>
                <span class="free-con">{{ `剩余${lastFlow}%` }}</span>
              </div>
            </div>
          </template>
          <!-- 如果没有选择了互斥组-->
          <template v-else>
            <div class="mutu-con">
              <span class="is-mutu">是否互斥实验：</span>
              <el-radio-group v-model="mutuType" :disabled="true">
                <el-radio :label="0">否</el-radio>
                <el-radio :label="1">是</el-radio>
              </el-radio-group>
            </div>
          </template>
        </template>
        <template v-else>
          <div class="mutu-con">
            <span class="is-mutu">是否互斥实验：</span>
            <el-radio-group v-model="mutuType" @change="handelchangeMutu3">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
            <div class="s-wrap">
              <el-select ref="select" v-model="addData.newExclusiveId" v-if="mutuType == 1" @change="freeFlowChange">
                <el-option v-for="item in mutuList" :key="item.id" :value="item.id" :label="item.displayName"
                  :disabled="item.freeFlow == 0">
                  <span class="op-name" style="float: left">{{ item.displayName }}</span>
                  <span class="freeflow" style="float: right">{{ item.freeFlow / 10 }} %</span>
                </el-option>
              </el-select>
              <span class="free-con" v-if="mutuType == 1 && addData.newExclusiveId">
                {{ `剩余${lastFlow}%` }}
              </span>
            </div>
            <el-button type="text" class="create-mutu" @click="createMutu" v-if="mutuType == 1">
              新建互斥组
            </el-button>
          </div>
        </template>
      </template>
      <section v-if="(isAdd || isEdit) && mutuType == 1 && lastFlow" style="padding-left: 255px;margin-top: 6px">
        <el-alert class="duration-alert"
          :title="lastFlow === 100 ? '加入互斥组后，当前实验将和互斥组下其他实验瓜分100%的流量，请谨慎选择。' : `当前互斥组流量已使用${100 - lastFlow}%，还剩余${lastFlow}%可用，请谨慎选择。`"
          type="warning" effect="dark" />
      </section>
    </section>
    <div class="mutu-con process" @click="handleDocumentClick('experimentFlow')">
      <span>实验流量：</span>
      <div class="silder-con">
        <el-slider :disabled="isCheck" v-model="addData.flow" :min="0" :max="100" :step="0.1" />
      </div>

      <div class="processInput">
        <el-input-number controls-position="right" v-model="addData.flow" :min="0" :max="maxFlow" :precision="1"
          :step="0.1" :disabled="isCheck" />
        <span>%</span>
      </div>
      <span class="flow-set-btn" @click="showModal">算一算开多少流量合适</span>
    </div>
    <h3 class="target-cu-set">选取受众</h3>
    <section @click="handleDocumentClick('filterRule')">
      <section v-if="!isAllowedEdit() && !addData.filterRule.length" class="no-data">
        无
      </section>
      <section class="rule-container" v-else>
        <div class="line-con" v-if="addData.filterRule.length > 1">
          <span>或</span>
        </div>
        <div v-for="(frule, index) in addData.filterRule" :key="index" class="rule-item">
          <section class="top">
            <p class="and-sign" v-show="frule.filterList.length > 1"><span>且</span></p>
            <div v-if="frule.filterList.length" class="rule-con">
              <div class="detail-set" :class="[frule.filterList.length > 1 ? 'detail-set-b' : '']"
                v-for="(flist, idx) in frule.filterList" :key="idx">
                <!-- 受众规则名称(最右边) -->
                <el-select v-model="flist.filterConfId" size="small" filterable
                  :disabled="!isAllowedEdit() || flist.filterConfId === 0" class="s-w filter-select" placeholder="请选择"
                  :popper-append-to-body="false" @change="(val) => handleChangeFilterKeyName(val, flist)">
                  <el-option v-for="(fitem, findex) in getFilterList(flist)" :key="findex" :label="fitem.displayName"
                    :value="fitem.id" :disabled="fitem.disabled">
                    <el-popover :open-delay="300" placement="right" trigger="hover">
                      <div>
                        <div style="min-width: 200px">
                          <h3 style="font-size: 14px; font-weight: 600">参数名: {{ fitem.displayName }}</h3>
                          <p style="padding: 20px 0">属性名：{{ fitem.keyName }}</p>
                          <p style="font-size: 14px" v-if="fitem.keyType === 1">值类型: String</p>
                          <p style="font-size: 14px" v-if="fitem.keyType === 2">值类型: Boolean</p>
                          <p style="font-size: 14px" v-if="fitem.keyType === 4">值类型: Float</p>
                        </div>
                      </div>
                      <p slot="reference">{{ fitem.displayName }}</p>
                    </el-popover>
                  </el-option>
                </el-select>
                <!-- 受众规则比较逻辑(中间) -->
                <el-select :disabled="!isAllowedEdit()" v-model="flist.keyType" collapse-tags class="s-w"
                  placeholder="请选择" @change="(val) => handleChangeKeyType(val, flist)">
                  <el-option v-for="(sItem, sIndex) in flist.symbolList" :key="sIndex" :label="sItem.displayName"
                    :value="sItem.type"></el-option>
                </el-select>
                <!-- 受众规则输入值(最左边) -->
                <!-- 当keyType选择了等于或不等于的时候 -->
                <template v-if="flist.keyType === 1 || flist.keyType === 2">
                  <!-- 人群包 -->
                  <template v-if="flist.useEnum && flist.filterType === 2">
                    <el-select :disabled="!isAllowedEdit() || flist.filterConfId === '' || !flist.keyType"
                      v-model="flist.keyValue" multiple filterable class="s-w s-slect" placeholder="">
                      <el-option v-for="(item, kIndex) in flist.keyValueEnums" :key="kIndex" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </template>
                  <template v-else>
                    <el-select v-model="flist.keyValue" v-if="isIpType(flist.filterConfId)" multiple filterable remote
                      :remote-method="getIpListOptions" :disabled="!isAllowedEdit()">
                      <el-option v-for="item in ipList" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-select v-else :disabled="!isAllowedEdit() || flist.filterConfId === '' || !flist.keyType"
                      v-model="flist.keyValue" :multiple="flist.keyValueType !== 2" filterable class="s-w s-slect"
                      :allow-create="flist.keyValueType !== 2" default-first-option placeholder="请选择或创建后选择"
                      @change="(val) => handleKeyValueChange(val, flist, idx)">
                      <el-option v-for="item in getKeyValueEnums(flist)" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                    </el-select>
                  </template>
                </template>
                <template v-else>
                  <el-input-number v-if="flist.keyValueType === 4"
                    :disabled="!isAllowedEdit() || flist.filterConfId === '' || !flist.keyType"
                    controls-position="right" v-model="flist.keyValue" placeholder="请输入"
                    @change="(val) => handleKeyValueChange(val, flist, idx)"></el-input-number>
                  <el-input v-else :disabled="!isAllowedEdit() || flist.filterConfId === '' || !flist.keyType"
                    class="s-w s-slect el-select u-input" v-model="flist.keyValue" placeholder="请输入"
                    @change="(val) => handleKeyValueChange(val, flist, idx)" type="text"></el-input>
                </template>
                <span v-if="isAllowedEdit()" class="sigle-de el-icon-delete"
                  @click="handleDelFilterRule(index, idx)"></span>
              </div>
            </div>
          </section>
          <div class="op-con">
            <p class="op-add">
              <el-button v-if="isAllowedEdit()" type="text" @click="handleAddFilterRule(index, 1)">
                <i class="e-icon el-icon-plus"></i>
                添加过滤规则
              </el-button>
              <el-button v-if="isAllowedEdit()" type="text" @click="handleAddFilterRule(index, 2)">
                <i class="e-icon el-icon-plus"></i>
                用户分群
              </el-button>
            </p>
            <p class="op-delete">
              <el-button type="text" v-if="isAllowedEdit()" @click="handleDelRule(index)">
                <i class="e-icon el-icon-delete"></i>
                删除
              </el-button>
            </p>
          </div>
        </div>
      </section>
      <el-button v-if="isAllowedEdit()" type="default" size="small" class="add-rule" @click="handleAddRule"
        icon="el-icon-circle-plus">
        添加受众规则
      </el-button>
    </section>
    <h3 class="target-cu-set">用户属性变更</h3>
    <div class="c-flow-con" @click="handleDocumentClick('userAttributeChange')">
      <span>用户属性变更：</span>
      <el-radio-group v-model="addData.isResetUser" :disabled="statusIsRun() || statusIsAwaitPub() || isCheck">
        <el-radio :label="1">进组出组</el-radio>
        <el-radio :label="0">进组不出组</el-radio>
      </el-radio-group>
      <p>{{ this.addData.isResetUser === 0 ? "用户在命中实验后，即使属性或分群发生变化，也将始终保持之前的命中结果。" : "根据用户属性和分群变化，实时判断用户能否命中实验。" }}</p>
    </div>
    <compute-flow-dialog v-if="isShowDialog" :dialogVisible.sync="isShowDialog"></compute-flow-dialog>
    <create-mutu-dialog v-if="isShowMutuDialog" :dialogVisible.sync="isShowMutuDialog"
      @afterCreateMutu="afterCreateMutu"></create-mutu-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ComputeFlowDialog from './compute-flow-dialog.vue';
import CreateMutuDialog from './create-mutu-dialog.vue';
import ModifyDialog from './metric-dialog/index.vue';
export default {
  name: 'Exmetric',
  components: {
    ModifyDialog,
    CreateMutuDialog,
    ComputeFlowDialog
  },
  props: {
    exType: Number,
    current: Number
  },
  data() {
    return {
      seltedData: [],
      metricData1: [],
      modifyVisible: false,
      metricId: 0,
      CI: '95%',
      lastFlow: 0,
      tableList: [],
      hasOpAdded: false,
      labelPosition: 'top',
      options: [], // 受众规则
      formLabel: '选择关注指标',
      maxFlow: 100,
      mutuType: 0, // 是否互斥
      isCheck: this.$route.query.type === 'check',
      isAdd: this.$route.query.type === 'add',
      isEdit: this.$route.query.type === 'edit',
      isExtend: this.$route.query.isExtend === '1',
      isCopy: this.$route.query.subType === 'copy' || false,
      isCloudControl: this.$route.query.ex === '5' || false,
      relationFlowMap: [],
      isShowDialog: false,
      isShowMutuDialog: false,
      ipList: []
    };
  },
  computed: {
    ...mapGetters(['addData', 'metricData', 'appList', 'mutuList', 'filerList'], 'message'),
    tableData() {
      if (this.isAdd) {
        const data = [];
        this.addData.selectedMetric &&
          this.addData.selectedMetric.forEach((item) => {
            this.metricData.forEach((met) => {
              if (item === met.id) {
                data.push(met);
              }
            });
          });
        return data;
      }
    },
    tableData1() {
      if (this.isEdit || this.isCheck) {
        const data = [];
        this.seltedData &&
          this.seltedData.forEach((item) => {
            this.metricData.forEach((met) => {
              if (item === met.id) {
                data.push(met);
              }
            });
          });
        return data;
      }
    }
  },
  created() {
    if (this.exType === 5) {
      this.formLabel = '选择关注指标（云控实验暂不支持指标，如有需求请线下联系研发同学）';
    }
  },
  watch: {
    'addData.indicators': {
      handler(a, b) {
        if ((this.isEdit && a) || (this.isCheck && a)) {
          //this.seltedData
          const selectedMetric = []; // 选中的id
          for (let i = 0, len = this.addData.indicators.length; i < len; i++) {
            const curr = this.addData.indicators[i];
            selectedMetric.push(curr.indId); // select选中的
            //availablTableData.push(curr); // 展示的列表
          }
          this.seltedData = selectedMetric;
        }
      }
    },
    'addData.exclusiveId': {
      handler: function (newVal, oldVal) {
        if (!this.isAdd && newVal) {
          this.mutuType = 1;
        }
      }
    },
    'addData.newExclusiveId': {
      handler: function (newVal, oldVal) {
        if (!this.isAdd && newVal) {
          this.mutuType = 1;
        }
      }
    },
    mutuList: {
      handler: function (newVal, oldVal) {
        if (this.isEdit || this.isCheck) {
          this.freeFlowChange();
          if (
            (this.addData.exclusiveId && this.statusIsPub()) ||
            (this.addData.exclusiveId && this.statusIsAwaitPub())
          ) {
            this.freeFlowChange1();
          }
        }
        // 当切换实验第一步中的实验类型、账号体系、业务线的时候需要重置
        if (this.isAdd && newVal && this.current != 1) {
          this.mutuType = 0;
          this.maxFlow = 100;
          this.lastFlow = 0;
        }

        if (this.isChild && this.relationFlowMap.length && this.addData.newExclusiveId) {
          // 查找当前编辑子实验 互斥组名称
          if (this.isAdd || this.statusIsDraft()) {
            const obj = this.mutuList.find((f) => f.id == this.addData.newExclusiveId) || {};
            obj.displayName &&
              (this.relationFlowMap[this.relationFlowMap.length - 1].exclusiveDisplayName =
                obj.displayName);
          }
        }
      }
    },
    metricData: {
      handler(a, b) {
        // if (this.isEdit && a) {
        //   //this.seltedData
        //   const tableData = []; // 选中的id
        //   if (this.addData.indicators.length) {
        //     this.metricData.forEach((item) => {
        //       this.addData.indicators.forEach((list) => {
        //         if (list.indId == item.id) {
        //           tableData.push(item);
        //         }
        //       });
        //     });
        //   }
        //   this.tableData1 = tableData;
        // }
      }
    }
  },
  mounted() { },
  methods: {
    handleDocumentClick(key) {
      this.$eventbus.$emit('exp-document', key);
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() { // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() { // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() { // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() { // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() { // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() { // 暂停状态
      return this.addData.status === 6;
    },
    statusIsDongJie() {
      console.log(' this.addData.status === 7;', this.addData.status === 7);
      return this.addData.status === 7;
    },
    handleKeyValueChange(val, flist, idx) {
      //this.$message.warning(Object.prototype.toString.call(val) + " --> " + JSON.stringify(val));
      if (typeof val === 'object') {
        flist.keyValue = val;
      } else {
        flist.keyValue = [val + ''];
      }
    },
    // 当改变受众规则中的keyType
    handleChangeKeyType(val, flist) {
      flist.keyValue = [];
    },
    // 当改变受众规则的keyName时候
    handleChangeFilterKeyName(val, flist) {
      this.reset(flist);
      let filterList = this.filerList.filterList;
      for (let i = 0, len = filterList.length; i < len; i++) {
        const curr = filterList[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.useEnum = curr.useEnum;
          flist.keyValueEnums = curr.keyName === 'ip' ? this.ipList : (curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []));
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    reset(flist) {
      flist.keyType = '';
      flist.keyValue = [];
    },
    getChangeFilterKeyName(val, flist) {
      let filterList = this.filerList.filterList;
      for (let i = 0, len = filterList.length; i < len; i++) {
        const curr = filterList[i];
        if (curr.id === val) {
          flist.keyValueType = curr.keyType;
          flist.filterType = curr.filterType;
          flist.useEnum = curr.useEnum;
          flist.keyValueEnums = curr.keyType === 2 ? [{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }] : (curr.keyValueEnums ? curr.keyValueEnums : []);
          flist.symbolList = curr.symbols && curr.keyType !== 2 ? curr.symbols : [{ type: 1, displayName: '等于' }, { type: 2, displayName: '不等于' }];
          break;
        }
      }
    },
    // 添加受众规则
    handleAddRule() {
      this.$store.commit('addRule');
    },
    // 删除受众规则
    handleDelRule(index) {
      this.$store.commit('delRule', { index });
    },
    // 删除过滤规则
    handleDelFilterRule(index, idx) {
      this.$store.commit('delFilterRule', { index, idx });
    },
    // 添加过滤规则
    handleAddFilterRule(index, type) {
      let item = {};
      if (type === 2) {
        this.getChangeFilterKeyName(0, item);
        item.filterConfId = 0;
      }
      const data = this.$store.commit('addFilterRule', { index, item });
    },
    getFilterList(item) {
      const index = item.filterConfId === 0 ? 0 : 1;
      const list = (this.filerList.filterList || []).map(item => {
        return {
          ...item,
          disabled: item.id === 0
        };
      });
      return list || [];
    },
    isAllowedEdit() {
      // 3 发布, 5 待发布, 6 暂停 7 冻结
      let isNonRunEditCode = this.isEdit && (![3, 6, 7].includes(this.addData.status) || this.isCloudControl);
      return this.isAdd || isNonRunEditCode || this.isExtend;
    },
    handelchangeMutu3() {
      if (this.mutuType === 1 && this.mutuList.length) {
        this.addData.newExclusiveId = this.mutuList[0].id;
        this.freeFlowChange();
        return;
      } else {
        this.addData.newExclusiveId = '';
        this.addData.exclusiveId = '';
      }
      this.maxFlow = 100;

    },
    handelchangeMutu() {
      if (this.mutuType === 1 && this.mutuList.length) {
        this.addData.newExclusiveId = this.mutuList[0].id;
        this.freeFlowChange();
        return;
      } else {
        this.addData.newExclusiveId = '';
      }
      this.maxFlow = 100;
    },
    freeFlowChange() {
      for (let i = 0; i < this.mutuList.length; i++) {
        let current = this.mutuList[i];
        if (current.id === this.addData.newExclusiveId) {
          this.maxFlow = current.freeFlow / 10;
          this.lastFlow = current.freeFlow / 10;
          if (this.addData.flow > this.maxFlow) {
            this.addData.flow = this.maxFlow;
          }
          break;
        }
      }

    },
    // 当选中互斥实验后，样本总量收到互斥实验最大值的控制
    freeFlowChange1() {

      for (let i = 0; i < this.mutuList.length; i++) {

        let current = this.mutuList[i];
        if (current.id == this.addData.exclusiveId) {
          //debugger
          this.maxFlow = current.freeFlow / 10;
          this.lastFlow = current.freeFlow / 10;
          if (this.addData.flow > this.maxFlow) {
            this.addData.flow = this.maxFlow;
          }
          break;
        }
      }
    },
    showModal() {
      this.isShowDialog = true;
    },
    // 创建完毕后需要将新创建的选中
    afterCreateMutu(data) {
      //debugger
      if (this.mutuType === 1) {
        // debugger
        //this.freeFlowChange();
        this.lastFlow = 100;
        this.maxFlow = 100;
      }
      this.addData.newExclusiveId = data;
    },
    afterCreateMutu1(data) {
      if (this.mutuType === 1) {
        this.addData.exclusiveId = data;
        this.freeFlowChange1();
      }
    },
    // 创建互斥组
    createMutu() {
      this.isShowMutuDialog = true;
    },
    editMtric(id) {
      this.metricId = id;
      this.modifyVisible = true;
    },
    createMetric() {
      this.metricId = 0;
      this.modifyVisible = true;
    },
    fetchMmetricList() {
      this.$store.dispatch('getMetricList');
    },
    handleComfirm() { },
    changeMetric(val) {
      let temp = [];
      // this.metricData.forEach((item) => {
      //   if (item.isMust === 1) {
      //     temp.push(item.id);
      //   }
      // });
      // this.hasOpAdded = true;
      // if(!temp.includes(val)){

      // }
    },
    changeMetric1(val) {
      // debugger;
      // const temp = [];
      // val.forEach((item)=>{
      //   this.metricData.forEach((list)=>{
      //     if(item == list.id){
      //       temp.push(list);
      //     }
      //   })
      // })
      // this.tableData1 = temp;
    },
    // 删除指标项
    removeTag(val) {
      this.handelRemoveTagDetail(val);
    },
    // 移除指标的具体逻辑
    handelRemoveTagDetail(val) {
      console.log(val);
      let temp = [];
      this.metricData.forEach((item) => {
        if (item.isMust === 1) {
          temp.push(item.id);
        }
      });
      if (this.isAdd) {
        const selectedMetric = this.addData.selectedMetric;
        // 兼容在select删除
        if (temp.includes(val) && !selectedMetric.includes(val)) {

          this.$message.warning('该指标为必看指标,不可删除');
          const pos = temp.indexOf(val);
          this.addData.selectedMetric.splice(pos, 0, val); // 此处是为了把删除值插入回去
          return;

        }
        // 兼容从列表中直接删除
        if (temp.includes(val)) {

          this.$message.warning('该指标为必看指标,不可删除');
          return;

        }

        this.$store.commit('delMetricTableData', val);
        // if (!temp.includes(val) && selectedMetric.length === temp.length) {
        //
        //   this.$message.warning('至少需要一个可以计算的置信指标作为核心指标');
        //   this.addData.selectedMetric.push(val);
        // }
      } else {
        const selectedMetric = this.seltedData;
        // 兼容在select删除
        if (temp.includes(val) && !selectedMetric.includes(val)) {

          this.$message.warning('该指标为必看指标,不可删除');
          const pos = temp.indexOf(val);
          this.seltedData.splice(pos, 0, val); // 此处是为了把删除值插入回去
          return;

        }
        // 兼容从列表中直接删除
        if (temp.includes(val)) {

          this.$message.warning('该指标为必看指标,不可删除');
          return;

        }

        selectedMetric.forEach((item, index) => {
          if (item === val) {
            selectedMetric.splice(index, 1);
          }
        });
        // this.tableData1.forEach((item,index)=>{
        //   if(item.id == val){
        //     this.tableData1.splice(index,1)
        //   }
        // })
      }
    },
    //
    handleRemove(val) {
      this.handelRemoveTagDetail(val);
    },
    getKeyValueEnums(flist) {
      const { filterConfId } = flist;
      const list = this.filerList.filterList || [];
      const target = list.find(item => item.id === filterConfId) || {};
      return flist.keyValueEnums || [];
    },
    async getIpListOptions(keyword) {
      const list = await this.$service
        .get('FILTERIPLIST', {
          keyword
        });
      this.ipList = list.map(item => {
        return {
          label: item.name,
          value: item.name
        };
      });
    },
    isIpType(id) {
      const list = this.filerList.filterList || [];
      const target = list.find(item => item.id === id) || {};
      return target.keyName === 'ip';
    }
  }
};
</script>

<style lang="less" scoped>
.steps-content {
  margin-top: 40px;
  padding: 24px 20px;
  border: 1px solid #e1e2e6;
  border-radius: 2px;
  overflow: auto;

  .no-data {
    margin-left: 130px;
    font-size: 14px;
  }

  >h3 {
    padding: 0 0 12px 0;
    font-size: 16px;
    font-weight: 700;
  }

  .mutu-con {
    display: flex;
    align-items: center;
    // padding: 0 35px 20px;
    margin-top: 8px;

    &>span {
      display: inline-block;
      width: 130px;
      padding-right: 10px;
      text-align: right;
      box-sizing: border-box;
      font-size: 14px;

      &.flow-set-btn {
        margin-left: 10px;
        cursor: pointer;
        color: #42c57a;
        width: 150px;
      }
    }

    /deep/ .el-select {
      margin-left: 20px;
    }
  }

  .process {
    display: flex;
    align-items: center;

    h3 {
      margin-right: 10px;
      min-width: 90px;
    }

    .silder-con {
      width: 40%;
      max-width: 450px;
    }

    .processInput .el-input-number {
      width: 100px;
      margin: 0 10px;
    }
  }

  .create-mutu {
    padding-left: 20px;
  }

  .flow-con {
    display: flex;
    align-items: center;
    padding-bottom: 20px;

    h3 {
      padding-bottom: 0;
    }
  }

  .add-con {
    display: flex;
    justify-content: flex-end;
  }

  .version-create {
    display: flex;
    overflow: hidden;
    margin: 20px 0 0;
    padding: 0 35px;
  }

  .rule-container {
    position: relative;
    margin-left: 45px;

    .line-con {
      position: absolute;
      top: 12px;
      bottom: 12px;
      width: 12px;
      // background-color: #42c57a;
      border: 2px solid #42c57a;
      border-right: none;

      span {
        position: absolute;
        left: -13px;
        background: #42c57a;
        color: white;
        padding: 5px;
        top: 50%;
        font-size: 14px;
        line-height: 14px;
        transform: translateY(-50%);
        border-radius: 4px;
        font-weight: bold;
      }
    }

    .rule-item {
      margin-left: 32px;
      margin-bottom: 8px;

      .top {
        display: flex;
        flex-direction: row;

        .rule-con {
          display: flex;
          width: 100%;
          padding: 0px;
          position: relative;
          flex-direction: column;
        }

        .and-sign {
          width: 12px;
          border: 2px solid #42c57a;
          border-right: none;
          position: relative;
          margin-top: 12px;
          margin-bottom: 12px;
          margin-right: 8px;

          span {
            position: absolute;
            left: -13px;
            background: #42c57a;
            color: white;
            padding: 5px;
            top: 50%;
            font-size: 14px;
            line-height: 14px;
            transform: translateY(-50%);
            border-radius: 4px;
            font-weight: bold;
          }

        }
      }

      .detail-set {
        margin: 5px 0;

        .el-select {
          margin-right: 12px;
        }
      }

      .detail-set:hover .sigle-de {
        display: inline-block;
      }
    }
  }

  .target-cu-set {
    margin-top: 30px;
  }

  .c-flow-con {

    &>span {
      display: inline-block;
      width: 130px;
      padding-right: 10px;
      box-sizing: border-box;
      text-align: right;
      font-size: 14px;
    }

    &>p {
      padding-left: 130px;
      margin-top: 8px;
    }
  }


  .add-rule {
    margin-left: 40px;

    /deep/ i {
      // color: white;
      // border-radius: 50%;
      // background: #42c57a;
      // box-sizing: border-box;
      // padding: 2px;
      font-size: 14px;
      color: #42c57a;
    }
  }


  .s-slect {
    width: 300px;
  }

  .sigle-de {
    display: none;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
  }

  .op-con {
    .e-icon {
      margin-right: 6px;
      margin-top: -3px;
    }
  }

  .s-slect {
    min-width: 500px;
  }

  .metric-con {
    margin: 20px 0 0;
    padding: 0 35px;
    font-size: 14px;
  }

  .create-metric {
    margin-left: 10px;
  }

  /dee/ .el-table th .core-span {
    font-size: 14px;
  }

  /deep/ .el-tag.is-disabled {
    //background-color: transparent;
    border-color: #f0f1f5;
    color: #999;
    cursor: not-allowed;
  }

  /deep/ .op-add .el-button.is-disabled,
  /deep/ .op-delete .el-button.is-disabled {
    background-color: transparent;
  }

  /deep/ .el-textarea.is-disabled .el-textarea__inner,
  /deep/ .el-input.is-disabled .el-input__inner {
    //background-color: transparent;
    border-color: #e1e2e6;
    color: #999;
    //cursor: not-allowed;
  }

  /deep/ .el-tooltip__popper.is-light {
    div {
      font-size: 14px;
    }
  }

  .ci-con {
    display: flex;
  }

  .ci-val {
    margin: 0 2px;
  }

  /deep/ .op-switch.is-disabled .el-switch__core,
  /deep/ .op-switch.is-disabled .el-switch__label {
    background-color: rgb(155, 156, 158);
    border-color: rgb(155, 156, 158);
    cursor: pointer;
  }

  /deep/ .el-switch.is-disabled.is-checked .el-switch__core,
  /deep/ .op-switch.is-disabled.is-checked .el-switch__label {
    border-color: #42c57a;
    background-color: #42c57a;
  }

  /deep/ .op-btn span {
    font-size: 14px;
  }
}

/deep/ .el-table {
  font-size: 14px;
}

/deep/ .el-select .el-tag {
  font-size: 14px;
}
</style>
