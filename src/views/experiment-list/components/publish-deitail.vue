<template>
  <div class="section-container">
    <section>
      <h3 class="form-title">
        <p>发布人群</p>
        <section style="width: auto;margin-left: 12px;">
          <el-alert
            title="不设置发布范围，将对全部用户进行发布！"
            type="warning"
            :closable="false"
          ></el-alert>
        </section>
      </h3>
    </section>
    <shouzhong-rule :isView="isView || isEdit" v-model="form.filterRule"></shouzhong-rule>
    <section>
      <h3 class="form-title" style="margin-top: 36px">发布规则</h3>
      <el-form ref="formBase" :rules="rules" :model="form" label-width="130px">
        <el-form-item prop="version.versionId" label="发布策略">
          <el-select
            v-model="form.version.versionId"
            placeholder="请选择发布策略"
            :disabled="isView || isEdit"
          >
            <el-option
              v-for="(item, index) in pushVersionList"
              :disabled="item.disabled"
              :key="item.versionId + index"
              :label="item.displayName"
              :value="item.versionId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="defaultVersion.versionId" label="默认策略">
          <el-select
            v-model="form.defaultVersion.versionId"
            placeholder="请选择默认策略"
            :disabled="isView || isEdit"
          >
            <el-option label="无" :value="0"></el-option>
            <el-option
              v-for="(item, index) in defaultVersionList"
              :disabled="item.disabled"
              :key="item.versionId + index"
              :label="item.displayName"
              :value="item.versionId"
            ></el-option>
          </el-select>
          <el-popover trigger="hover" placement="top" width="230">
            <p class="popoverContent" style="font-size: 14px">
              默认策略选择“无”，分流结果将返回不命中。
            </p>
            <i
              slot="reference"
              class="el-icon-question basic-set-icon-question"
              style="margin-left: 8px"
            ></i>
          </el-popover>
        </el-form-item>
        <el-form-item prop="duration" label="发布周期">
          <el-input-number
            v-model="form.duration"
            placeholder="请输入"
            :disabled="isView"
            :max="90"
            :min="0"
            step-strictly
            :precision="0"
            :step="1"
          ></el-input-number>
          <el-alert
            title="发布周期最多可以设置90天！"
            type="warning"
            :closable="false"
            style="display: inline;margin:0 8px"
          ></el-alert>
          <el-popover trigger="hover" placement="top" width="230">
            <p class="popoverContent" style="font-size: 14px">
              发布到期后，实验将终止运行，所有用户将执行代码兜底策略。发布计划并不能完全替代产品代码发版，建议实验得出优胜版本后，尽快完成产品正式发版。
            </p>
            <i
              slot="reference"
              class="el-icon-question basic-set-icon-question"
              style="margin-left: 8px"
            ></i>
          </el-popover>
        </el-form-item>
        <h3 class="form-title" style="margin-top: 36px">
          <p>发布比例</p>
          <section style="width: auto;margin-left: 12px;">
            <el-alert
              title="实验中已命中发布策略的用户，仍将保持在发布分组，发布比例计算将不包含这部分用户"
              type="warning"
              :closable="false"
            ></el-alert>
          </section>
        </h3>
        <el-form-item prop="flow" label="发布比例">
          <el-slider
            :disabled="isView"
            v-model="form.flow"
            :show-tooltip="false"
            :min="0"
            :max="100"
            :step="0.1"
            style="display: inline-block;width: 400px;vertical-align: middle;margin: 0 12px;position: relative; top: -2px"
          ></el-slider>
          <el-input-number
            :disabled="isView"
            controls-position="right"
            v-model="form.flow"
            :min="minFlow"
            :max="100"
            :precision="1"
            :step="0.1"
          />
          <span style="margin: 0 6px">%</span>
          <section style="width: 100%;font-size: 12px;">比例设置生效后，将不支持再下调！</section>
        </el-form-item>
        <h3 class="form-title" style="margin-top: 36px">
          <p>未命中发布策略用户发布方式</p>
        </h3>
        <el-form-item prop="pushType" label="方式选择">
          <el-radio-group v-model="form.pushType" :disabled="isView || isEdit">
            <el-radio
              v-if="isJinzubuchuzu"
              :label="1"
              style="margin-bottom:10px;padding-top: 10px"
              :value="1"
            >
              运行阶段已进组用户继续执行运行阶段命中的分组策略，运行阶段未进组用户命中默认策略（推荐）
            </el-radio>
            <el-radio :label="2" style="width: 100%;">都将执行默认策略</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </section>
  </div>
</template>

<script>
import ShouzhongRule from './shouzhong-rule.vue';
import { mapGetters } from 'vuex';
import { formatFormData } from '@/utils/util';
import { pick } from 'lodash';
const validator = (rule, value, callback) => {
  if (!value) {
    callback(new Error(rule.message));
  } else {
    callback();
  }
};
export default {
  name: 'PublishDetail',
  components: {
    ShouzhongRule
  },
  props: {
    isView: Boolean,
    id: Number | String,
    pushId: Number
  },
  data() {
    return {
      form: {
        filterRule: [],
        version: {},
        defaultVersion: {},
        pushType: 1,
        duration: 0,
        flow: 0
      },
      rules: {
        'version.versionId': [{ required: true, message: '请选择发布策略', trigger: 'blur' }],
        duration: [{ required: true, message: '请设置发布周期', trigger: 'blur', validator }],
        flow: [{ required: true, message: '请设置发布比例', trigger: 'blur', validator }]
      },
      firstSetValue: true,
      minFlow: 0
    };
  },
  computed: {
    ...mapGetters(['addData'], 'message'),
    isAdd() {
      return !this.pushId;
    },
    isEdit() {
      const res = this.pushId && !this.isView;
      return res;
    },
    isDisabledStatus() {
      return this.isView;
    },
    versionList() {
      return this.addData.versionList ? this.addData.versionList.slice(1) : [];
    },
    isJinzubuchuzu() {
      return this.addData ? this.addData.isResetUser !== 1 : false;
    },
    pushVersionList() {
      const { defaultVersion = {} } = this.form;
      const { versionId } = defaultVersion;
      if (!versionId) {
        return this.versionList;
      }
      const list = this.versionList.map(item => {
        return {
          ...item,
          disabled: item.versionId === versionId
        };
      });
      return list;
    },
    defaultVersionList() {
      const { version = {} } = this.form;
      const { versionId } = version;
      if (!versionId) {
        return this.versionList;
      }
      const list = this.versionList.map(item => {
        return {
          ...item,
          disabled: item.versionId === versionId
        };
      });
      return list;
    }
  },
  created() {
    if (!this.isView) {
      this.$store.dispatch('getExpDetail', {
        id: this.id
      });
    }
    if (this.pushId) {
      this.getPushDetail();
    }
  },
  watch: {
    isJinzubuchuzu: {
      handler(val) {
        if (!val) {
          this.form.pushType = 2;
        }
      },
      immediate: true
    },
    addData: {
      handler(val) {
        if (+val.id !== +this.id) {
          return;
        }
        if (!this.pushId && this.firstSetValue && val.id) {
          this.firstSetValue = false;
          setTimeout(() => {
            const formatData = (val.filterRule || []).map(item => {
              return {
                ...item,
                filterList: item.filterList.map(sub => {
                  const { keyValueType, keyValue } = sub;
                  return {
                    ...sub,
                    keyValue: keyValueType === 2 && Array.isArray(keyValue) ? keyValue[0] : keyValue
                  };
                })
              };
            });
            this.form.filterRule = formatData;
          }, 100);
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    async getPushDetail() {
      const res = await this.$service.get('VIEWPUSHEXP', {
        experimentId: this.id,
        pushId: this.pushId
      });
      const keys = Object.keys(this.form);
      const data = pick(res, keys);
      this.form = {
        ...data,
        flow: data.flow ? data.flow / 10 : 0
      };
      this.minFlow = this.form.flow;
      const { filterRule } = this.form;
      filterRule.forEach(rule => {
        rule.filterList.forEach(list => {
          let kvx = JSON.parse(list.keyValue);
          if (typeof kvx === 'object') {
            list.keyValue = Array.from(kvx).map(x => x + '');
          } else {
            list.keyValue = ['' + list.keyValue];
          }
        });
      });
    },
    delRedundantKey(obj, key) {
      if (obj.hasOwnProperty(key)) {
        delete obj[key];
      }
    },
    async submit() {
      // 校验受众规则是否都有值
      this.form.filterRule.forEach(rule => {
        rule.filterList.forEach(list => {
          if (!list.keyValue.length) {
            this.$message.error('无效受众规则');
            throw new Error('无效受众规则');
          }
        });
      });
      await this.$refs.formBase.validate();
      const text = '实验一经发布，原实验配置将不支持编辑，且原实验不支持继承，请谨慎操作。';
      await this.$confirm(text, '提示');
      const data = {
        ...this.form,
        flow: this.form.flow * 10,
        experimentId: this.id,
        pushId: this.pushId || undefined
      };
      data.filterRule.forEach(rule => {
        rule.filterList.forEach(list => {
          if (Array.isArray(list.keyValue)) {
            list.keyValue = JSON.stringify(Array.from(list.keyValue).map(kv => kv + ''));
          } else {
            list.keyValue = JSON.stringify([list.keyValue + '']);
          }
          this.delRedundantKey(list, 'symbolList');
          this.delRedundantKey(list, 'useEnum');
          this.delRedundantKey(list, 'keyValueEnums');
        });
      });
      const submitData = formatFormData(data);
      const url = this.pushId ? 'EDITPUSHEXP' : 'PUSHEXP';
      await this.$service.post(url, submitData, { allback: 0, needLoading: true }).then(res => {
        this.$message.success('发布成功！');
      });
    }
  }
};
</script>

<style lang="less" scoped>
.section-container {
  // min-height: 80vh;
  // max-height: calc(100vh - 210px);
  overflow: auto;
}
</style>
