<template>
  <Page class="container" :breadcrumbList="[]" :hasPagination="false">
    <section class="header">
      <section>
        <i class="el-icon-arrow-left" @click="handleGoBack(true)"></i>
        <span class="page-title">{{ title }}</span>
      </section>
    </section>
    <section class="main">
      <el-steps class="ex_steps" :active="current" finish-status="success" align-center>
        <el-step v-for="item in stepData" :title="item.title" :icon="item.icon" :class="stepClassObj(item.index)"
          @click.native="stepClick(item.index)" :key="item.index"></el-step>
      </el-steps>
      <section class="content-container" style="height: calc(100vh - 300px);overflow: hidden;">
        <ex-baseInfo v-show="current === 0"></ex-baseInfo>
        <ex-metric ref="exMetric" v-show="current === 1" :exType="Number(ex)" :current="current"></ex-metric>
        <ex-solution v-show="current === 2" :current="current"></ex-solution>
        <ex-sample v-show="current === 3" :current="current"></ex-sample>
        <ExDocument />
      </section>
    </section>
    <div class="op-btns">
      <el-button v-if="current !== 0" @click="onPrevious">上一步</el-button>
      <el-button v-if="!isLast()" @click="onNextStep" type="primary">
        下一步
      </el-button>
      <!-- 非代发布状态 -->
      <template>
        <el-button v-if="isLast()" @click="handleSubmit(2)" type="primary">
          提交&调试
        </el-button>
        <el-button v-if="isLast()" @click="handleSubmit(1)" type="primary">
          保存草稿
        </el-button>
        <el-button @click="handleGoBack(true)" type="" class="go-back-btn" v-if="!isCheck">
          取消
        </el-button>
      </template>
    </div>
  </Page>
</template>

<script>
import Page from '@/components/common/page/index.vue';
import ExBaseInfo from './components/ex-baseInfo';
import ExMetric from './components/ex-metric';
import ExSolution from './components/ex-solution';
import ExSample from './components/ex-sample';
import ExHistory from './components/history';
import ExpReport from './report';
import { mapGetters } from 'vuex';
import * as _ from 'lodash';
import { getUrlByLocation } from '../../utils/util';
import ExDocument from './components/ex-document.vue';
export default {
  name: 'AddExperiment',
  components: {
    Page,
    ExBaseInfo,
    ExMetric,
    ExSolution,
    ExSample,
    ExHistory,
    ExpReport,
    ExDocument
  },
  async beforeRouteLeave(to, from, next) {
    if (this.isCheck || !this.needRouterConfirm || to.path === '/feat/list/detail') {
      next();
      return;
    }
    const action = await this.$confirm(
      `点击确定将放弃页面已填写内容，返回实验列表，确定返回吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).catch(() => {
      return 'cancel';
    });
    if (action === 'confirm') {
      next();
    }
  },
  data() {
    return {
      //seltedData: [],
      // 已选步骤
      stepSuc: [0],
      breadcrumbList: ['ExperimentList', 'addExp'],
      current: 0,
      stepData: [
        { index: 0, title: '实验基本信息', icon: '' },
        { index: 1, title: '设置生效策略', icon: '' },
        { index: 2, title: '配置实验版本', icon: '' }
        // {index: 3, title: '查看', icon: ''},
      ],
      tab: '1',
      expId: this.$route.query.id,
      parentId: this.$route.query.parentId,
      isAdd: this.$route.query.type === 'add' || false,
      isEdit: this.$route.query.type === 'edit' || false,
      isCheck: this.$route.query.type === 'check' || false,
      isCopy: this.$route.query.subType === 'copy' || false,
      isExtend: this.$route.query.isExtend === '1' || false,
      ex: this.$route.query.ex, // 实验类型：1编程实验 2可视化实验 5:云控实验
      correctId: this.$route.query.correctId || '', // 从固化新建实验
      ops: {
        1: {
          opName: '暂停',
          opTip: '暂停成功',
          data: {
            status: 6
          },
          api: 'SET_STATUS',
          warningTip: '暂停实验后会没有用户命中实验，确定暂停实验吗？'
        },
        2: {
          opName: '开始',
          opTip: '开始成功',
          warningTip: '开启实验后，进组用户可实时查看。确定开始吗？',
          api: 'START_TEST'
        },
        3: {
          opName: '继续',
          opTip: '继续成功',
          warningTip: '确定继续实验吗？',
          data: {
            status: 3
          },
          api: 'SET_STATUS'
        },
        4: {
          opName: '删除',
          opTip: '删除成功',
          warningTip: '删除的实验将从系统移除，删除后无法查看相关信息，确定删除吗？',
          api: 'DELETE_TEST'
        },
        5: {
          opName: '结束',
          opTip: '结束成功',
          warningTip: '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？',
          api: 'OVER_TEST'
        },
        6: {
          opName: '冻结',
          opTip: '冻结成功',
          warningTip: '冻结实验后，除已命中实验用户外，不再有新增用户命中实验。确定冻结实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 7
          }
        },
        7: {
          opName: '解冻',
          opTip: '解冻成功',
          warningTip: '确定解冻实验吗？',
          api: 'SET_STATUS',
          data: {
            status: 3
          }
        }
      },
      needRouterConfirm: false
    };
  },

  computed: {
    ...mapGetters(['addData', 'whiteList', 'metricData', 'statusMap'], 'message'),
    stepClassObj(val) {
      return val => {
        return {
          stepSuc: this.stepSuc.includes(val),
          stepErr: !this.stepSuc.includes(val)
        };
      };
    },
    isChild() {
      return !!this.$route.query.parentId;
    },
    title() {
      if (this.isCheck) {
        return this.addData.displayName;
      }
      const isCreateChild = this.isAdd && this.$route.query.parentId && !this.correctId;
      if (isCreateChild) {
        return '创建子实验';
      }
      if (this.isExtend) {
        return '实验继承';
      }
      if (this.isEdit && !this.isCopy) {
        return '编辑实验';
      }
      return '新增实验';
    },
    exstatus() {
      const status = this.addData.status;
      const clientType = this.addData.clientType;
      let statusR = {};
      const statusMap = {
        1: {
          name: '草稿',
          type: 'default'
        },
        2: {
          name: '调试中',
          type: 'default'
        },
        5: {
          name: '待发布',
          type: 'default'
        },
        3: {
          name: '发布中',
          type: 'success'
        },
        4: {
          name: '已结束',
          type: 'info'
        },
        6: {
          name: '已暂停',
          type: 'warning'
        },
        7: {
          name: '已冻结',
          type: 'warning'
        }
      };
      statusR = statusMap[status] || {};
      return statusR;
    }
  },
  watch: {},
  created() {
    if (!this.isCloudControl()) {
      console.log('hahaha0');
      this.stepData.push({ index: 3, title: '选择指标', icon: '' });
    }
    this.parentId && this.$store.commit('setFatherId', this.parentId);
    // 获取应用列表
    this.$store.dispatch('getAppList');
    // 直接从实验列表页面点击添加按钮过来创建实验
    // const isSimpleCreate = this.isAdd && !this.$route.query.parentId && !this.correctId;
    // if (isSimpleCreate) {
    //   this.$store.dispatch('getFilterList');
    //   this.$store.dispatch('getMutuList');
    //   this.$store.dispatch('getWhiteList');
    //   this.$store.dispatch('getMetricList');
    // }
    // 从列表中点击创建子实验按钮过来创建子实验
    const isCreateChild = this.isAdd && this.$route.query.parentId && !this.correctId;
    if (isCreateChild) {
      //debugger;
      this.$store.dispatch('getFatherExpDetail', { id: this.parentId, isCreateChild });
    }
    // 从固化列表过来创建实验
    const isCorrectCreate = this.isAdd && this.correctId;
    if (isCorrectCreate) {
      this.$store.dispatch('getExpDetail', {
        id: this.expId,
        parentId: this.parentId,
        correctId: this.correctId,
        isCopy: this.isCopy
      });
    }
    // 编辑或查看
    if (this.$route.query.id) {
      this.$store.dispatch('getExpDetail', {
        id: this.expId,
        parentId: this.parentId,
        correctId: this.correctId,
        isCopy: this.isCopy
      });
    }
    if (this.isCheck) {
      this.current = 3;
    }
  },
  methods: {
    isLast() {
      const last = this.isCloudControl() ? 2 : 3;
      return this.current === last;
    },
    statusAwaitRun() {
      return this.statusIsAwaitPub() || this.statusIsDebug();
    },
    statusIsRun() {
      return this.statusIsPub() || this.statusIsPause() || this.statusIsDongJie();
    },
    statusIsDraft() {
      // 草稿状态
      return this.addData.status === 1;
    },
    statusIsDebug() {
      // 调试状态
      return this.addData.status === 2;
    },
    statusIsPub() {
      // 发布状态
      return this.addData.status === 3;
    },
    statusIsStop() {
      // 结束状态
      return this.addData.status === 4;
    },
    statusIsAwaitPub() {
      // 待发布状态
      return this.addData.status === 5;
    },
    statusIsPause() {
      // 暂停状态
      return this.addData.status === 6;
    },
    statusIsDongJie() {
      // 冻结状态
      return this.addData.status === 7;
    },
    showGudingFeature() {
      const data = this.addData;
      const version = data.versionList ? data.versionList[0] : {};
      return data.type === 1 && version.featureList && version.featureList.length === 1;
    },
    isCloudControl() {
      return +this.ex === 5;
    },
    // 点击每一步骤
    stepClick(val) {
      if (this.stepSuc.includes(val) === true) {
        this.current = val;
      }
    },
    // 验证第一步中的邮箱
    validateEmails() {
      let flag = true;
      this.addData.myEmails.forEach(item => {
        if (!item.value) {
          flag = false;
        }
      });
      return flag;
    },
    // 验证第一步
    validateOneStep() {
      let isRight = true;
      const isCloudControl = +this.ex === 5;
      if (!this.addData.displayName) {
        this.$message.warning('实验名称不能为空！');
        isRight = false;
        return isRight;
      }
      // 运控实验不校验实验时长
      if (!isCloudControl && !this.addData.duration) {
        this.$message.warning('实验时长不能为空！');
        isRight = false;
        return isRight;
      }
      if (!this.addData.appKey) {
        this.$message.warning('业务线不能为空！');
        isRight = false;
        return isRight;
      }
      if (this.addData.isExpireNotice && !this.addData.dingToken) {
        this.$message.warning('请输入钉钉token');
        isRight = false;
        return isRight;
      }
      if (!this.addData.createUser) {
        this.$message.warning('请选择负责人');
        isRight = false;
        return isRight;
      }
      // if (!this.addData.labelList.length) {
      //   this.$message.warning('请选择或输入实验标签');
      //   isRight = false;
      //   return isRight;
      // }
      return true;
    },
    isNumeric(str) {
      const num = Number(str);
      return !isNaN(num) && typeof num === 'number';
    },
    validateSecondStep() {
      let isRight = true;
      if (!this.addData.flow) {
        this.$message.warning('流量分配不能为空');
        isRight = false;
        return isRight;
      }
      // 如果是从发布中的实验来创建继承实验
      if (this.isExtend && this.addData.originFlow > this.addData.flow) {
        this.$message.warning(
          `继承实验，样本总量不支持调小，原样本总量为${this.addData.originFlow}%，当前样本总量为${this.addData.flow}%，请重新调整`
        );
        isRight = false;
        return isRight;
      }
      // 发布中、已暂停、已冻结不支持调小
      if (
        (this.statusIsPub() || this.statusIsDongJie() || this.statusIsPause()) &&
        this.addData.originFlow > this.addData.flow
      ) {
        this.$message.warning(
          `实验发布后，样本总量不支持调小，原样本总量为${this.addData.originFlow}%，当前样本总量为${this.addData.flow}%，请重新调整`
        );
        isRight = false;
        return isRight;
      }
      const filterRuleLen = this.addData.filterRule.length;
      if (filterRuleLen) {
        for (let i = 0; i < filterRuleLen; i++) {
          let currtFilter = this.addData.filterRule[i];
          const filterList = currtFilter.filterList;

          for (let j = 0, len = filterList.length; j < len; j++) {
            const curList = filterList[j];
            const isNumber = typeof curList.keyValue === 'number';
            if (
              curList.filterConfId === '' ||
              !curList.keyType ||
              (!isNumber && !curList.keyValue.length)
            ) {
              this.$message.warning('无效的受众规则');
              isRight = false;
              return isRight;
            }
            const { keyValue, keyValueType } = curList;
            if (keyValueType === 4 && Array.isArray(keyValue)) {
              const pass = keyValue.every(item => this.isNumeric(item));
              if (!pass) {
                isRight = false;
                this.$message.warning('受众参数类型错误！');
                return isRight;
              }
            }
          }
        }
      }
      return true;
    },
    // 验证第三步
    validateThirdStep() {
      let isRight = true;
      const versions = this.addData.versionList,
        trueVersions = versions.slice(1),
        trueVerLen = trueVersions.length;
      const selectedWhiteList = [];
      let sum = 0;
      for (let j = 0; j < trueVerLen; j++) {
        selectedWhiteList.push(...trueVersions[j].mywhitelist);
        sum += 1 * trueVersions[j].flow;
      }
      if (isNaN(sum)) {
        this.$message.warning('版本流量不能为空!');
        isRight = false;
        return isRight;
      }
      // 为了简单解决前端精度问题
      if (sum <= 99.9) {
        this.$message.warning('各版本流量占比总和不足100%，请重新分配');
        isRight = false;
        return isRight;
      }

      if (this.isRepeat(selectedWhiteList)) {
        this.$message.warning('每个版本的白名单不能重复');
        isRight = false;
        return isRight;
      }
      let displayNameArr = [];
      if (this.ex == 1 || this.ex == 5) {
        const versions = this.addData.versionList,
          params = versions[0].featureList,
          paramsLen = params.length,
          typeTest = /^\w+$/, //只能为字母、数字、下划线
          trueVersions = versions.slice(1),
          trueVerLen = trueVersions.length;
        for (let i = 0; i < paramsLen; i++) {
          if (!params[i].keyName) {
            this.$message.warning('参数名不能为空');
            isRight = false;
            return isRight;
          }
          // 参数名只能为字母、数字、下划线
          if (!typeTest.test(params[i].keyName)) {
            this.$message.warning('实验版本参数名只能为字母、数字、下划线');
            isRight = false;
            return isRight;
          }
        }
        for (let j = 0; j < trueVerLen; j++) {
          const featureList = trueVersions[j].featureList;
          for (let k = 0, featLen = featureList.length; k < featLen; k++) {
            if (featureList[k].keyValue === '') {
              this.$message.warning('参数值不能为空');
              isRight = false;
              return isRight;
            } else if (featureList[k].keyType === 5 && !this.isJson(featureList[k].keyValue)) {
              this.$message.warning('请输入正确的JSON');
              isRight = false;
              return isRight;
            }
          }
        }

        let tempArr = [],
          newArr = [];
        for (let j = 0; j < trueVerLen; j++) {
          const featureList = trueVersions[j].featureList;
          const name = trueVersions[j].displayName;
          displayNameArr.push(name);
          for (let k = 0, featLen = featureList.length; k < featLen; k++) {
            if (tempArr.indexOf(featureList[k].keyName) === -1) {
              newArr.push({
                keyName: featureList[k].keyName,
                keyType: featureList[k].keyType,
                list: [featureList[k].keyValue]
              });
              tempArr.push(featureList[k].keyName);
            } else {
              for (let j = 0; j < newArr.length; j++) {
                if (newArr[j].keyName == featureList[k].keyName) {
                  newArr[j].list.push(featureList[k].keyValue);
                }
              }
            }
          }
        }
      }

      for (let i = 0, len = displayNameArr.length; i < len; i++) {
        // 除了boolean 类型外，其他都不允许重复
        const name = displayNameArr[i];
        if (!name) {
          this.$message.warning('实验版本名称不可为空');
          isRight = false;
          return isRight;
        }
        if (this.isRepeat(displayNameArr)) {
          this.$message.warning('每个版本的版本名称不能重复');
          isRight = false;
          return isRight;
        }
      }
      if (this.isChild) {
        // 关联父实验不能为空
        const isRight = trueVersions.every(item => item.relationIds.length);
        if (!isRight) {
          this.$message.warning('请选择父实验版本');
          return false;
        }
      }
      return true;
    },
    // 验证第四步
    validateFourthStep() {
      return true;
    },
    isJson(str) {
      if (typeof str == 'string') {
        try {
          var obj = JSON.parse(str);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          console.log('error：' + str + '!!!' + e);
          return false;
        }
      } else {
        this.$message.error('请输入字符串');
      }
    },
    // 下一步
    onNextStep() {
      let flag = false;
      //debugger;
      if (this.current === 0) {
        flag = this.validateOneStep();
        //debugger;
        if (!flag) {
          return;
        }
      }
      if (this.current === 1) {
        flag = this.validateSecondStep();
        if (!flag) {
          return;
        }
      }
      if (this.current === 2) {
        flag = this.validateThirdStep();
        if (!flag) {
          return;
        }
      }
      if (this.current === 3) {
        flag = this.validateFourthStep();
        //debugger
        if (!flag) {
          return;
        }
      }
      if (flag) {
        this.stepSuc.push(++this.current);
      }
    },
    isRepeat(arr) {
      var hash = {};
      for (var i in arr) {
        if (hash[arr[i]]) {
          return true;
        }
        hash[arr[i]] = true;
      }
      return false;
    },
    // 上一步
    onPrevious() {
      if (--this.current === 0) {
        this.current = 0;
      }
    },

    // 返回列表页面
    handleGoBack(flag = false) {
      this.needRouterConfirm = flag;
      this.$router.push({
        path: '/exp-manage/list'
      });
    },
    validateBeforeSubmit() {
      const flag1 = this.validateOneStep();
      const flag2 = this.validateSecondStep();
      const flag3 = this.validateThirdStep();
      const flag4 = this.validateFourthStep();
      //debugger;
      return flag1 && flag2 && flag3 && flag4;
    },
    // 处理提交
    handleSubmit(status) {
      // 提交之前再次验证
      const flag = this.validateBeforeSubmit();
      //debugger;
      if (flag) {
        // status == 1: 保存草稿
        // status == 2; 提交&测试
        status = status == 3 && this.isExtend ? 5 : status;
        const data = this.formatAddData(
          [6, 7].includes(this.addData.status) ? this.addData.status : status
        );
        // data.id = undefined;
        const formData = this.formatFormData(data);
        // console.log(formData.get('filterRule'));
        // 如果是在新增时候
        if (!this.isEdit || this.isCopy) {
          if (status == 2) {
            this.$confirm(`确认提交&测试吗？`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.handleCreateExp(formData, status);
              })
              .catch(e => {
                console.log(e);
                this.$message({
                  type: 'info',
                  message: '已取消'
                });
              });
          } else {
            // 如果用户点击了保存草稿
            this.handleCreateExp(formData, status);
          }
        } else {
          // 如果是在编辑
          if (status == 2 || status == 3 || status == 5) {
            if (this.isExtend) {
              this.$confirm(`确认提交继承实验吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  this.handleCreateExp(formData, status);
                })
                .catch(e => {
                  console.log(e);
                  this.$message({
                    type: 'info',
                    message: '已取消'
                  });
                });
            } else {
              const tip = status == 2 ? '确认提交&测试吗？' : '确认修改吗？';
              this.$confirm(tip, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              })
                .then(() => {
                  this.handleUpdateExp(formData, status);
                })
                .catch(e => {
                  console.log(e);
                  this.$message({
                    type: 'info',
                    message: '已取消'
                  });
                });
            }
          } else {
            // 如果用户点击了保存草稿

            this.handleUpdateExp(formData, status);
          }
        }
      }
    },
    // 创建实验
    handleCreateExp(formData, status) {
      const tip = {
        1: '添加草稿成功',
        2: '提交&测试成功',
        3: '提交成功'
      };
      this.$service
        .post('ADD_TEST', formData, { allback: 0, needLoading: true })
        .then(res => {
          this.$message.success(`${tip[status]}`);
          this.handleGoBack();
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 编辑实验
    handleUpdateExp(formData, status) {
      const tip = {
        1: '保存草稿成功',
        2: '提交&测试成功',
        3: '确认修改成功',
        5: '确认修改成功'
      };
      this.$service
        .post('VIEW_TEST', formData, { allback: 0, needLoading: true })
        .then(res => {
          this.$message.success(`${tip[status]}`);
          this.handleGoBack();
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 组装提交数据
    formatAddData(status) {
      const data = _.cloneDeep(this.addData);
      if (this.isEdit) {
        const ref = this.$refs['exMetric'];
        //debugger;
        const seltedData = this.$refs['exMetric'].seltedData;
        //debugger;
        const temp = [];
        //const selectedMetric = seltedData;
        const indicators1 = this.addData.indicators;
        //  seltedData.forEach((item)=>{
        //    indicators.forEach(list=>{
        //      if(item !== list.indId){
        //        indicators.splice
        //      }
        //    })
        //  })
        const indicators = [];
        const selectedMetric = seltedData;
        selectedMetric.forEach(item => {
          indicators.push({
            indId: item,
            isCore: 0
          });
        });
        indicators.forEach(item => {
          indicators1.forEach(list => {
            if (item.indId === list.indId) {
              item.id = list.id;
            }
          });
        });

        data.indicators = indicators;
        //debugger;
        // seltedData.forEach((item) => {
        //   indicators.forEach((list) => {
        //     if (item == list.indId && list.id) {
        //       //item.id = list.id;
        //       temp.push({
        //         id: list.id,
        //         indId: item, //指标ID
        //         isCore: 0, // 是否为核心指标 0:否 1:是
        //       });
        //     }else {
        //        temp.push({
        //         indId: item, //指标ID
        //         isCore: 0, // 是否为核心指标 0:否 1:是
        //       });
        //     }
        //   });
        // });
        // data.indicators = temp;
        //debugger;
      }
      // 从固化过来新建实验
      if (this.correctId) {
        data.correctId = this.correctId;
      }
      // 创建继承实验
      if (this.isExtend) {
        data.extendsExperimentId = data.id;
      }
      data.type = this.ex;
      data.status = status;
      data.isExpireNotice = data.isExpireNotice ? 1 : 0; //
      data.isLongNotice = data.isLongNotice ? 1 : 0;
      if (data.newExclusiveId) {
        data.exclusiveId = data.newExclusiveId;
        // debugger;
      }
      data.isFlowAvg = data.isFlowAvg ? 1 : 0; //版本之间是否平均分配流量0否1是
      data.flow = data.flow ? data.flow * 10 : 0; //版本流量前端是按照100来展示，但是后端用的是1000
      data.versionList.splice(0, 1); // 由于从第二个开始才是真正的版本对象，第一个是为了展示参数部分
      data.versionList.forEach(version => {
        version.featureList.forEach(feature => {
          feature.keyValue = feature.keyValue + '';
        });
      });
      data.filterRule.forEach(rule => {
        rule.filterList.forEach(list => {
          if (Array.isArray(list.keyValue)) {
            list.keyValue = JSON.stringify(Array.from(list.keyValue).map(kv => kv + ''));
          } else {
            list.keyValue = JSON.stringify([list.keyValue + '']);
          }
          this.delRedundantKey(list, 'symbolList');
          this.delRedundantKey(list, 'useEnum');
          this.delRedundantKey(list, 'keyValueEnums');
        });
      });
      // 组装指标数据
      if (this.isAdd) {
        const indicators = [];
        const selectedMetric = this.addData.selectedMetric;
        selectedMetric.forEach(item => {
          indicators.push({
            indId: item,
            isCore: 0
          });
        });
        data.indicators = indicators;
      }
      if (this.isChild) {
        data.togetherEnd = data.togetherEnd ? 1 : 0;
        data.parentId = this.parentId / 1;
      } else {
        data.togetherEnd = 0;
        data.parentId = 0;
        data.versionList.forEach(version => {
          version.relationIds = [];
        });
      }

      //debugger;
      // 处理第一步邮件接收人
      // data.emails = data.myEmails.map((item) => `${item.value}@zuoyebang.com`);

      this.delRedundantKey(data, 'newExclusiveId');
      this.delRedundantKey(data, 'selectedMetric');
      this.delRedundantKey(data, 'myEmails');
      return this.formatWhiteList(data);
    },
    // 组装白名单数据
    formatWhiteList(data) {
      data.versionList.forEach(item => {
        item.flow = parseInt(item.flow * 10);
        this.delRedundantKey(item, 'fixedName');
        item.whitelist = item.mywhitelist.map(item => ({
          whitelistId: item
        }));
        this.delRedundantKey(item, 'mywhitelist');
      });
      return data;
    },
    // 删除冗余字段
    delRedundantKey(obj, key) {
      if (obj.hasOwnProperty(key)) {
        delete obj[key];
      }
    },
    // 转换成formData格式提交给后台
    formatFormData(data) {
      const formData = new FormData();
      for (let key in data) {
        if (typeof data[key] === 'object') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      }
      return formData;
    },
    //具体动作
    handleOperate(row, op) {
      const opData = op.data ? op.data : {};
      const data = {
        experimentId: row.id,
        appKey: row.appKey,
        ...opData
      };
      const formData = this.formatFormData(data);
      this.$service.post(op.api, formData, { allback: 0, needLoading: true }).then(res => {
        this.$message.success(op.opTip);
        window.location.reload();
      });
    },
    // 确认操作
    handleComfirm(type) {
      const op = this.ops[type];
      const row = this.addData;
      let info = '';
      const childExpName =
        row.childExperimentList && row.childExperimentList.length
          ? row.childExperimentList.map(v => v.displayName)
          : '';
      if (type == 1) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验暂停后，暂停期间父实验无进组用户，关联父实验版本的子实验版本也将没有进组用户，确定暂停吗？`
          : '暂停后不再有正常用户命中实验，仅对白名单用户生效；暂停期间该实验无进组用户，确定暂停吗？';
      } else if (type == 6) {
        info = childExpName
          ? `当前实验关联了子实验：${childExpName};实验冻结后，父实验不再有新用户进组，已经命中父实验用户，仍作为关联父实验版本的子实验受众继续命中子实验，确定冻结吗？`
          : '冻结实验后，保持当前进组数据，不再有新用户进组，确定冻结实验吗？';
      } else if (type == 5) {
        info = childExpName
          ? `当前的实验关联了子实验：${childExpName};结束实验后，子实验也将同时结束，父实验和子实验结果将为最终结果，结束的实验无法重新启动，确定结束吗？`
          : '结束实验后，当前实验结果将为最终结果，而且无法重新启动实验，确定结束吗？';
      } else if (type == 2 && row.status == 5) {
        info = row.extendsNotice || '开启后原实验自动结束，确定开启继承实验吗？';
      } else {
        info = op.warningTip;
      }
      if (info) {
        this.$confirm(info, '提示', {
          customClass: 'ex-table-message',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.handleOperate(row, op);
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
          });
      } else {
        this.handleOperate(row, op);
      }
    },
    // 诊断实验
    goDiagnosis() {
      const row = this.addData;
      const hash = '/exp-manage/diagnosis';
      const params = {
        accSystem: row.accountSystem,
        id: row.id,
        name: encodeURIComponent(row.displayName)
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 固化feature
    turnFeature() {
      const row = this.addData;
      const hash = '/feat/list/detail';
      const params = {
        id: row.correctId > 0 ? row.correctId : '', // 固化id
        experimentId: row.id, //实验id
        type: row.correctId > 0 ? 'detail' : ''
      };
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    // 创建继承实验
    createExtendExp() {
      const row = this.addData;
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        ex: row.type,
        isExtend: 1
      };
      if (this.parentId / 1) {
        params.parentId = this.parentId;
      }
      this.$router.push({ path: hash, query: params });
    },
    // 组装实验历史需要的部分数据
    assembleHistoryDay(row) {
      localStorage.setItem('displayName', row.displayName);
      const statusVal = this.handleStatus(row.status);
      localStorage.setItem('status', statusVal);
    },
    handleStatus(status) {
      //const statusData = statusList;
      return this.statusMap[status];
    },
    createChildExp() {
      const row = this.addData;
      const hash = '/exp-manage/list/add';
      const params = {
        type: 'add',
        parentId: row.id,
        ex: row.type
      };
      this.$router.push({ path: hash, query: params });
    },
    // 编辑实验
    editExp() {
      const row = this.addData;
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        ex: row.type
      };
      if (this.parentId / 1) {
        params.parentId = this.parentId;
      }
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    copyExp() {
      const row = this.addData;
      this.assembleHistoryDay(row);
      const hash = '/exp-manage/list/edit';
      const params = {
        id: row.id,
        type: 'edit',
        subType: 'copy',
        ex: row.type
      };
      if (this.parentId / 1) {
        params.parentId = this.parentId;
      }
      const url = getUrlByLocation(hash, params);
      window.open(url);
    },
    getType(type) {
      console.log('type', type);
    }
  },
  beforeDestroy() {
    // 组件销毁前需要重置store中的addData，否则再次打开新建还有
    this.$store.commit('resetAddData');
  }
};
</script>

<style lang="less" scoped>
.container {
  overflow: auto;
  // height: calc(100vh - 100px);
  // padding: 24px;
  padding: 12px 16px;
  box-sizing: border-box;
  display: flex;

  /deep/ .page-container {
    padding-top: 0px;
    padding-bottom: 6px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .main {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .report-tab.container {
      height: auto;
    }

    .base-form {
      flex: 1;
      overflow: auto;
    }

    .content-container {
      display: flex;
      & > div {
        flex: 1;
        margin-top: 24px;
      }
    }
  }

  /deep/ .page-cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    margin: 0;
  }

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #e1e3e9;
    padding-bottom: 6px;

    section {
      display: flex;
      align-items: center;
    }

    .right {
      /deep/ i {
        // display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 4px;
        margin-right: 6px;
        display: inline-block;
      }

      .el-button {
        margin-left: 12px;
      }

      .icon-yunhang {
        // width: 18px;
        // height: 18px;
      }
    }

    .page-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 32px;
      margin: 0 8px;
    }

    i {
      width: 24px;
      height: 24px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .el-tag {

      // @zyb-red-1: #fa574b;
      // @zyb-red-2: #f9685d;
      // @zyb-red-3: #de4f43;
      // @zyb-orange-1: #f78502;
      // @zyb-orange-2: #f9b001;
      // @zyb-green-4: #42c57a;
      // @zyb-lake-blue: #00cccd;
      // @zyb-purple: #8276de;
      // @zyb-blue: #5392ff;
      &.el-tag--default {
        color: #5392ff;
        background-color: #d9ecff;
        border-color: #d9ecff;
      }
    }
  }
}

.ex_steps {
  margin-top: 36px;
}

.op-btns {
  padding-top: 10px;

  // .go-back-btn {
  //   float: right;
  // }
}

.stepSuc :hover {
  cursor: pointer;
}

.stepErr :hover {
  cursor: not-allowed;
}

/deep/ .el-step__head.is-success {
  color: #42c57a;
  border-color: #42c57a;
}

/deep/ .el-step__main .is-success {
  color: #42c57a;
}

/deep/ .el-step__icon {
  width: 32px;
  height: 32px;
}

/deep/ .el-step.is-horizontal .el-step__line {
  top: 15px;
}
</style>
